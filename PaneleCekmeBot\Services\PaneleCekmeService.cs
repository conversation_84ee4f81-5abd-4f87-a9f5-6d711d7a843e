using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PaneleCekmeBot.Models;

namespace PaneleCekmeBot.Services
{
    public interface IPaneleCekmeService
    {
        Task<bool> PaneleCekAsync(string cekimId);
        Task<List<bool>> PaneleCekBulkAsync(List<string> cekimIds);
        Task ProcessYeniTaleplerAsync(List<CekimTalebi> talepler);
    }

    public class PaneleCekmeService : IPaneleCekmeService
    {
        private readonly IHttpClientService _httpClient;
        private readonly ILoginService _loginService;
        private readonly ILogger<PaneleCekmeService> _logger;
        private readonly AppSettings _settings;

        public PaneleCekmeService(
            IHttpClientService httpClient,
            ILoginService loginService,
            ILogger<PaneleCekmeService> logger,
            IOptions<AppSettings> settings)
        {
            _httpClient = httpClient;
            _loginService = loginService;
            _logger = logger;
            _settings = settings.Value;
        }

        public async Task<bool> PaneleCekAsync(string cekimId)
        {
            if (string.IsNullOrEmpty(cekimId))
            {
                _logger.LogWarning("Geçersiz çekim ID: {CekimId}", cekimId);
                return false;
            }

            var retryCount = 0;
            var maxRetries = _settings.Bot.MaxRetryCount;

            while (retryCount <= maxRetries)
            {
                try
                {
                    _logger.LogDebug("Panele çekme işlemi başlatılıyor. ID: {CekimId}, Deneme: {Retry}", 
                        cekimId, retryCount + 1);

                    // Session kontrolü
                    if (!await _loginService.IsLoggedInAsync())
                    {
                        _logger.LogWarning("Session geçersiz, yeniden giriş yapılıyor...");
                        if (!await _loginService.LoginAsync())
                        {
                            _logger.LogError("Yeniden giriş başarısız");
                            return false;
                        }
                    }

                    // POST verisi hazırla
                    var postData = new Dictionary<string, string>
                    {
                        ["id"] = cekimId
                    };

                    // Panele çekme isteği gönder
                    var response = await _httpClient.PostAsync(_settings.Login.PaneleCekUrl, postData);

                    // Yanıtı kontrol et
                    if (string.IsNullOrEmpty(response))
                    {
                        _logger.LogWarning("Panele çekme isteğinden boş yanıt alındı. ID: {CekimId}", cekimId);
                        retryCount++;
                        continue;
                    }

                    response = response.Trim().ToLower();

                    if (response.Contains("ok") || response.Contains("başarılı") || response.Contains("success"))
                    {
                        _logger.LogInformation("✅ Panele çekme başarılı! ID: {CekimId}", cekimId);
                        return true;
                    }
                    else if (response.Contains("alınmış") || response.Contains("başka") || response.Contains("taken"))
                    {
                        _logger.LogWarning("⚠️ Çekim başka biri tarafından alınmış. ID: {CekimId}", cekimId);
                        return false; // Retry yapmaya gerek yok, başkası almış
                    }
                    else
                    {
                        _logger.LogWarning("Beklenmeyen yanıt alındı. ID: {CekimId}, Response: {Response}", 
                            cekimId, response);
                        retryCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Panele çekme işlemi sırasında hata. ID: {CekimId}, Deneme: {Retry}", 
                        cekimId, retryCount + 1);
                    retryCount++;
                }

                // Retry öncesi kısa bir bekleme
                if (retryCount <= maxRetries)
                {
                    await Task.Delay(100); // 100ms bekleme
                }
            }

            _logger.LogError("❌ Panele çekme başarısız (max retry aşıldı). ID: {CekimId}", cekimId);
            return false;
        }

        public async Task<List<bool>> PaneleCekBulkAsync(List<string> cekimIds)
        {
            if (cekimIds?.Any() != true)
            {
                return new List<bool>();
            }

            _logger.LogInformation("Toplu panele çekme işlemi başlatılıyor. Toplam: {Count}", cekimIds.Count);

            // Paralel işlem için task'ları oluştur
            var tasks = cekimIds.Select(async id =>
            {
                try
                {
                    return await PaneleCekAsync(id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Toplu işlemde hata. ID: {CekimId}", id);
                    return false;
                }
            });

            // Tüm task'ları paralel olarak çalıştır
            var results = await Task.WhenAll(tasks);

            var basariliSayisi = results.Count(r => r);
            _logger.LogInformation("Toplu panele çekme tamamlandı. Başarılı: {Basarili}/{Toplam}", 
                basariliSayisi, cekimIds.Count);

            return results.ToList();
        }

        public async Task ProcessYeniTaleplerAsync(List<CekimTalebi> talepler)
        {
            if (talepler?.Any() != true)
            {
                return;
            }

            _logger.LogInformation("🚀 Yeni talepler işleniyor. Toplam: {Count}", talepler.Count);

            // Hız için paralel işlem yap
            var tasks = talepler.Select(async talep =>
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                try
                {
                    var basarili = await PaneleCekAsync(talep.Id);
                    stopwatch.Stop();

                    if (basarili)
                    {
                        _logger.LogInformation("✅ İşlem başarılı! ID: {Id}, İsim: {Isim}, Tutar: {Tutar}, Süre: {Sure}ms", 
                            talep.Id, talep.Isim, talep.Tutar, stopwatch.ElapsedMilliseconds);
                    }
                    else
                    {
                        _logger.LogWarning("❌ İşlem başarısız! ID: {Id}, İsim: {Isim}, Tutar: {Tutar}, Süre: {Sure}ms", 
                            talep.Id, talep.Isim, talep.Tutar, stopwatch.ElapsedMilliseconds);
                    }

                    return basarili;
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    _logger.LogError(ex, "Talep işlenirken hata. ID: {Id}, Süre: {Sure}ms", 
                        talep.Id, stopwatch.ElapsedMilliseconds);
                    return false;
                }
            });

            // Tüm task'ları paralel çalıştır
            var results = await Task.WhenAll(tasks);

            var basariliSayisi = results.Count(r => r);
            _logger.LogInformation("📊 Talep işleme tamamlandı. Başarılı: {Basarili}/{Toplam}", 
                basariliSayisi, talepler.Count);
        }
    }
}
