using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PaneleCekmeBot.Models;

namespace PaneleCekmeBot.Services
{
    public interface ILoginService
    {
        Task<bool> LoginAsync();
        Task<bool> IsLoggedInAsync();
        Task LogoutAsync();
    }

    public class LoginService : ILoginService
    {
        private readonly IHttpClientService _httpClient;
        private readonly ILogger<LoginService> _logger;
        private readonly AppSettings _settings;
        private bool _isLoggedIn = false;

        public LoginService(
            IHttpClientService httpClient,
            ILogger<LoginService> logger,
            IOptions<AppSettings> settings)
        {
            _httpClient = httpClient;
            _logger = logger;
            _settings = settings.Value;
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                _logger.LogInformation("Giriş işlemi başlatılıyor...");

                // 1. Login sayfasını al ve token'ı çıkar
                var loginPageContent = await _httpClient.GetAsync(_settings.Login.LoginUrl);
                var token = ExtractTokenFromHtml(loginPageContent);

                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogError("Login sayfasından token alınamadı");
                    return false;
                }

                _logger.LogDebug("Token alındı: {Token}", token);

                // 2. Login POST isteği gönder
                var loginData = new Dictionary<string, string>
                {
                    ["login_username"] = _settings.Login.Username,
                    ["login_password"] = _settings.Login.Password,
                    ["token"] = token
                };

                var loginResponse = await _httpClient.PostAsync(_settings.Login.LoginUrl, loginData);

                // 3. Başarılı giriş kontrolü
                if (loginResponse.Contains("index.php") || loginResponse.Contains("dashboard") || 
                    !loginResponse.Contains("login") || loginResponse.Contains("çıkış"))
                {
                    _logger.LogInformation("Giriş başarılı!");
                    _isLoggedIn = true;

                    // PHPSESSID cookie'sini kontrol et
                    var sessionId = _httpClient.GetCookie("PHPSESSID");
                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        _logger.LogDebug("PHPSESSID cookie alındı: {SessionId}", sessionId);
                    }

                    return true;
                }
                else
                {
                    _logger.LogError("Giriş başarısız. Kullanıcı adı veya şifre hatalı olabilir.");
                    _logger.LogDebug("Login yanıtı: {Response}", loginResponse.Substring(0, Math.Min(500, loginResponse.Length)));
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Giriş işlemi sırasında hata oluştu");
                return false;
            }
        }

        public async Task<bool> IsLoggedInAsync()
        {
            try
            {
                if (!_isLoggedIn)
                    return false;

                // Session'ın hala geçerli olup olmadığını kontrol et
                var testResponse = await _httpClient.GetAsync(_settings.Login.ListeleUrl);
                
                // Eğer login sayfasına yönlendirildiyse session süresi dolmuş
                if (testResponse.Contains("login") && testResponse.Contains("password"))
                {
                    _logger.LogWarning("Session süresi dolmuş, yeniden giriş gerekiyor");
                    _isLoggedIn = false;
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Session kontrolü sırasında hata");
                _isLoggedIn = false;
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                _logger.LogInformation("Çıkış işlemi yapılıyor...");
                _httpClient.ClearCookies();
                _isLoggedIn = false;
                _logger.LogInformation("Çıkış tamamlandı");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çıkış işlemi sırasında hata");
            }
            await Task.CompletedTask;
        }

        private string? ExtractTokenFromHtml(string html)
        {
            try
            {
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                // Token input alanını bul
                var tokenInput = doc.DocumentNode
                    .SelectSingleNode("//input[@name='token']");

                if (tokenInput != null)
                {
                    var token = tokenInput.GetAttributeValue("value", "");
                    if (!string.IsNullOrEmpty(token))
                    {
                        return token;
                    }
                }

                // Alternatif olarak hidden input'ları kontrol et
                var hiddenInputs = doc.DocumentNode
                    .SelectNodes("//input[@type='hidden']");

                if (hiddenInputs != null)
                {
                    foreach (var input in hiddenInputs)
                    {
                        var name = input.GetAttributeValue("name", "");
                        if (name.ToLower().Contains("token") || name.ToLower().Contains("csrf"))
                        {
                            var value = input.GetAttributeValue("value", "");
                            if (!string.IsNullOrEmpty(value))
                            {
                                return value;
                            }
                        }
                    }
                }

                _logger.LogWarning("HTML içinde token bulunamadı");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token çıkarılırken hata oluştu");
                return null;
            }
        }
    }
}
