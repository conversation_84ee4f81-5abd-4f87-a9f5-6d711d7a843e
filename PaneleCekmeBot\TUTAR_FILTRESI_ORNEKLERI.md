# Tutar Filtresi Örnekleri

## 🎯 Temel Kullanım

### Sadece 500-2000 TL Arası Çekimler
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 500,
      "MaxTutar": 2000,
      "IgnoreInvalidAmounts": true
    }
  }
}
```

### Sad<PERSON>e <PERSON> (1000 TL ve üzeri)
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 1000,
      "MaxTutar": 999999,
      "IgnoreInvalidAmounts": true
    }
  }
}
```

### Sadece Küçük Tutarlar (100-500 TL arası)
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 100,
      "MaxTutar": 500,
      "IgnoreInvalidAmounts": true
    }
  }
}
```

## 🔧 Gelişmiş Senaryolar

### Tüm <PERSON> (Filtre Kapalı)
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": false
    }
  }
}
```

### Geç<PERSON><PERSON> Tu<PERSON> da <PERSON>ş<PERSON>
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 100,
      "MaxTutar": 5000,
      "IgnoreInvalidAmounts": false  // Geçersiz tutarları da işler
    }
  }
}
```

## 📊 Tutar Format Örnekleri

Bot aşağıdaki tutar formatlarını otomatik olarak tanır ve temizler:

### Desteklenen Formatlar
- `1500 TL` → 1500
- `1.500 TL` → 1500 (binlik ayırıcı)
- `1,500.50 TL` → 1500.50
- `₺1500` → 1500
- `1500₺` → 1500
- `1.500,50 TL` → 1500.50 (Türkçe format)

### Geçersiz Formatlar
- `ABC TL` → 0 (geçersiz)
- `TL` → 0 (geçersiz)
- `""` → 0 (boş)
- `null` → 0 (null)

## 🎮 Test Senaryoları

### Senaryo 1: Orta Gelir Seviyesi
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 200,
      "MaxTutar": 1500,
      "IgnoreInvalidAmounts": true
    }
  }
}
```
**Sonuç**: 200-1500 TL arası çekimler işlenir, geçersiz tutarlar göz ardı edilir.

### Senaryo 2: Yüksek Tutar Avcısı
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 2000,
      "MaxTutar": 50000,
      "IgnoreInvalidAmounts": true
    }
  }
}
```
**Sonuç**: Sadece 2000 TL ve üzeri yüksek tutarlar işlenir.

### Senaryo 3: Mikro Çekimler
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 10,
      "MaxTutar": 100,
      "IgnoreInvalidAmounts": true
    }
  }
}
```
**Sonuç**: Sadece 10-100 TL arası küçük tutarlar işlenir.

## 📈 Performans İpuçları

### Hızlı İşlem İçin
- Dar tutar aralığı seçin (örn: 1000-2000 TL)
- `IgnoreInvalidAmounts: true` kullanın
- Polling aralığını düşürün

### Güvenli İşlem İçin
- Geniş tutar aralığı seçin (örn: 100-5000 TL)
- `IgnoreInvalidAmounts: false` kullanın
- Polling aralığını yükseltin

## 🚨 Dikkat Edilmesi Gerekenler

1. **MinTutar > MaxTutar**: Bu durumda hiçbir çekim işlenmez
2. **Çok Dar Aralık**: Çok az çekim yakalayabilirsiniz
3. **Çok Geniş Aralık**: Çok fazla çekim yakalayıp sistem yükü oluşturabilir
4. **Geçersiz Tutarlar**: `IgnoreInvalidAmounts: false` dikkatli kullanın

## 🔍 Debug İpuçları

Tutar filtresi çalışmasını izlemek için:

```json
{
  "Logging": {
    "LogLevel": {
      "PaneleCekmeBot": "Debug"
    }
  }
}
```

Bu ayarla şu logları göreceksiniz:
```
[DBG] Tutar filtresi geçti. ID: 12345, Tutar: 1500 TL (1500), Aralık: 100-5000
[DBG] Tutar filtresi geçmedi. ID: 12346, Tutar: 50 TL (50), Aralık: 100-5000
[DBG] Geçersiz tutar göz ardı ediliyor. ID: 12347, Tutar: ABC TL
```
