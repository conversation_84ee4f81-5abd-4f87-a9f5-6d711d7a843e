# Panele Çekme Bot

Bu proje, belirli bir web sitesine otomatik giriş yaparak çekim taleplerini sürekli kontrol eden ve yeni talepleri hızlıca "panele çeken" bir C# .NET Core 8.0 uygulamasıdır.

## 🚀 Özellikler

- **Otomatik Login**: <PERSON><PERSON><PERSON><PERSON><PERSON> adı, şifre ve dinamik token ile güvenli giriş
- **Sürekli Monitoring**: 7/24 çalışarak yeni çekim taleplerini tespit eder
- **Hızlı İşlem**: Milisaniye seviyesinde hızlı panele çekme işlemi
- **Session Yönetimi**: PHPSESSID cookie'si ile oturum yönetimi
- **Paralel İşlem**: Birden fazla talebi aynı anda işleyebilir
- **Retry Mekanizması**: Başarısız istekleri otomatik olarak tekrar dener
- **Detaylı Loglama**: Tüm işlemlerin detaylı loglanması
- **Proxy Desteği**: Opsiyonel proxy kullanımı

## 📋 Gereksinimler

- .NET 8.0 SDK
- Windows 10/11 veya Windows Server
- İnternet bağlantısı

## ⚙️ Kurulum

1. **Projeyi klonlayın veya indirin**
2. **Bağımlılıkları yükleyin:**
   ```bash
   dotnet restore
   ```

3. **Ayarları yapılandırın:**
   `appsettings.json` dosyasını düzenleyin:
   ```json
   {
     "Login": {
       "Username": "KULLANICI_ADINIZ",
       "Password": "SIFRENIZ"
     }
   }
   ```

## 🔧 Konfigürasyon

### appsettings.json Ayarları

```json
{
  "Login": {
    "Username": "kullanici_adiniz",
    "Password": "sifreniz",
    "LoginUrl": "https://alfasystemsonline.com/panelx/",
    "ListeleUrl": "https://alfasystemsonline.com/panelx/ajax/listele_cekim_havuz.php",
    "PaneleCekUrl": "https://alfasystemsonline.com/panelx/ajax/panele_cek_islem.php"
  },
  "Bot": {
    "PollingIntervalMs": 1500,
    "RequestTimeoutMs": 10000,
    "MaxRetryCount": 3,
    "EnableDetailedLogging": true
  },
  "Proxy": {
    "Enabled": false,
    "Host": "proxy.example.com",
    "Port": 8080,
    "Username": "proxy_user",
    "Password": "proxy_pass"
  }
}
```

### Ayar Açıklamaları

- **PollingIntervalMs**: Çekim kontrolü aralığı (milisaniye)
- **RequestTimeoutMs**: HTTP istek timeout süresi
- **MaxRetryCount**: Başarısız istekler için maksimum deneme sayısı
- **EnableDetailedLogging**: Detaylı log çıktısı

## 🚀 Çalıştırma

### Development Modunda
```bash
dotnet run
```

### Production Build
```bash
dotnet build -c Release
dotnet run -c Release
```

### Windows Service Olarak Çalıştırma
```bash
# Publish
dotnet publish -c Release -o ./publish

# Service olarak kurulum (yönetici yetkisi gerekli)
sc create "PaneleCekmeBot" binPath="C:\path\to\publish\PaneleCekmeBot.exe"
sc start "PaneleCekmeBot"
```

## 📊 Loglama

Uygulama detaylı loglar üretir:

- **Information**: Genel bilgi mesajları
- **Warning**: Uyarı mesajları
- **Error**: Hata mesajları
- **Debug**: Detaylı debug bilgileri

### Log Örnekleri
```
2024-01-15 10:30:15.123 [INF] 🤖 Panele Çekme Bot başlatılıyor...
2024-01-15 10:30:16.456 [INF] ✅ Giriş başarılı!
2024-01-15 10:30:17.789 [INF] 🎯 Yeni çekim talepleri tespit edildi: 3 adet
2024-01-15 10:30:18.012 [INF] ✅ İşlem başarılı! ID: 12345, İsim: Test User, Tutar: 100 TL, Süre: 234ms
```

## 🔒 Güvenlik

- **Session Yönetimi**: PHPSESSID cookie'si ile güvenli oturum
- **Token Kontrolü**: Her login'de dinamik token kullanımı
- **Proxy Desteği**: IP gizleme için proxy kullanımı
- **Rate Limiting**: Aşırı istek gönderimini önleme

## ⚠️ Önemli Notlar

1. **Kullanıcı Bilgileri**: `appsettings.json` dosyasındaki kullanıcı bilgilerini mutlaka güncelleyin
2. **Polling Aralığı**: Çok düşük değerler IP ban riskine neden olabilir
3. **Proxy Kullanımı**: Yüksek trafikte proxy kullanımı önerilir
4. **Monitoring**: Uygulamayı sürekli izleyin ve logları kontrol edin

## 🛠️ Geliştirme

### Proje Yapısı
```
PaneleCekmeBot/
├── Models/
│   ├── AppSettings.cs
│   ├── CekimTalebi.cs
│   └── CekimListesiResponse.cs
├── Services/
│   ├── HttpClientService.cs
│   ├── LoginService.cs
│   ├── CekimMonitoringService.cs
│   ├── PaneleCekmeService.cs
│   └── PaneleCekmeBotWorker.cs
├── Program.cs
├── appsettings.json
└── README.md
```

### Kullanılan Teknolojiler
- **.NET 8.0**: Ana framework
- **Microsoft.Extensions.Hosting**: Background service
- **HtmlAgilityPack**: HTML parsing
- **Newtonsoft.Json**: JSON işlemleri
- **Microsoft.Extensions.Logging**: Loglama

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. Log dosyalarını kontrol edin
2. Ayarları doğrulayın
3. İnternet bağlantısını kontrol edin
4. Proxy ayarlarını kontrol edin (kullanıyorsanız)

## ⚖️ Lisans

Bu proje eğitim amaçlı geliştirilmiştir. Kullanım sorumluluğu kullanıcıya aittir.
