# Panele Çekme Bot - Kullanım Kılavuzu

## 🚀 Hızlı Başlangıç

### 1. Ayarları Yapılandırın
`appsettings.json` dosyasını açın ve aşağıdaki bilgileri güncelleyin:

```json
{
  "Login": {
    "Username": "GERÇEK_KULLANICI_ADINIZ",
    "Password": "GERÇEK_ŞİFRENİZ"
  }
}
```

### 2. Uygulamayı Çalıştırın
```bash
dotnet run
```

### 3. Logları İzleyin
Uygulama çalışırken aşağıdaki gibi loglar göreceksiniz:

```
🤖 Panele Çekme Bot v1.0
========================
2024-01-15 10:30:15.123 [INF] 🤖 Panele Çekme Bot başlatılıyor...
2024-01-15 10:30:16.456 [INF] 🔐 <PERSON><PERSON><PERSON> yapılıyor... (Deneme: 1/3)
2024-01-15 10:30:17.789 [INF] ✅ Giriş başarılı!
2024-01-15 10:30:18.012 [INF] 🚀 Bot başarıyla başlatıldı ve monitoring başlıyor...
```

## ⚙️ Gelişmiş Ayarlar

### Tutar Filtresi Ayarlama
```json
{
  "Bot": {
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 500,     // Minimum 500 TL
      "MaxTutar": 2000,    // Maksimum 2000 TL
      "IgnoreInvalidAmounts": true  // Geçersiz tutarları göz ardı et
    }
  }
}
```

### Polling Aralığını Ayarlama
```json
{
  "Bot": {
    "PollingIntervalMs": 1000  // 1 saniye (dikkatli kullanın!)
  }
}
```

### Proxy Kullanımı
```json
{
  "Proxy": {
    "Enabled": true,
    "Host": "proxy.example.com",
    "Port": 8080,
    "Username": "proxy_user",
    "Password": "proxy_pass"
  }
}
```

### Loglama Seviyesi
```json
{
  "Logging": {
    "LogLevel": {
      "PaneleCekmeBot": "Information"  // Debug, Information, Warning, Error
    }
  }
}
```

## 🎯 Beklenen Davranış

### Başarılı Çekim
```
[INF] 💰 Tutar Filtresi Aktif - Min: 100 TL, Max: 5000 TL, Geçersiz tutarları göz ardı et: True
[INF] 🎯 Yeni çekim talepleri tespit edildi: 2 adet
[INF] 🚀 Yeni talepler işleniyor. Toplam: 2
[INF] ✅ İşlem başarılı! ID: 12345, İsim: Test User, Tutar: 1500 TL, Süre: 234ms
[INF] ✅ İşlem başarılı! ID: 12346, İsim: Test User2, Tutar: 2000 TL, Süre: 189ms
[INF] 📊 Talep işleme tamamlandı. Başarılı: 2/2
```

### Tutar Filtresi Çalışması
```
[DBG] Tutar filtresi geçti. ID: 12345, Tutar: 1500 TL (1500), Aralık: 100-5000
[DBG] Tutar filtresi geçmedi. ID: 12346, Tutar: 50 TL (50), Aralık: 100-5000
[DBG] Geçersiz tutar göz ardı ediliyor. ID: 12347, Tutar: ABC TL
```

### Başka Biri Tarafından Alınmış
```
[WRN] ⚠️ Çekim başka biri tarafından alınmış. ID: 12347
```

### Session Süresi Dolmuş
```
[WRN] Session geçersiz, yeniden giriş yapılıyor...
[INF] ✅ Giriş başarılı!
```

## 🛠️ Sorun Giderme

### 1. Giriş Başarısız
- Kullanıcı adı ve şifrenizi kontrol edin
- İnternet bağlantınızı kontrol edin
- Site erişilebilir mi kontrol edin

### 2. JSON Parse Hatası
- Site yapısı değişmiş olabilir
- Endpoint URL'lerini kontrol edin

### 3. Çok Fazla İstek Hatası
- PollingIntervalMs değerini artırın (örn: 3000)
- Proxy kullanmayı düşünün

### 4. Session Sürekli Süresi Doluyor
- Çok fazla paralel istek olabilir
- MaxRetryCount değerini azaltın

### 5. Tutar Filtresi Çalışmıyor
- TutarFiltre.Enabled = true olduğundan emin olun
- MinTutar ve MaxTutar değerlerini kontrol edin
- Tutar formatını kontrol edin (TL, ₺ sembolleri otomatik temizlenir)

## 🔧 Performance Optimizasyonu

### Hızlı Çekim İçin (Yüksek Tutar)
```json
{
  "Bot": {
    "PollingIntervalMs": 1000,
    "RequestTimeoutMs": 5000,
    "MaxRetryCount": 1,
    "TutarFiltre": {
      "Enabled": true,
      "MinTutar": 1000,
      "MaxTutar": 999999
    }
  }
}
```

### Güvenli Kullanım İçin
```json
{
  "Bot": {
    "PollingIntervalMs": 3000,
    "RequestTimeoutMs": 15000,
    "MaxRetryCount": 3
  }
}
```

## 🚨 Önemli Uyarılar

1. **IP Ban Riski**: Çok sık istek göndermeyin
2. **Kullanıcı Bilgileri**: appsettings.json dosyasını güvenli tutun
3. **Monitoring**: Uygulamayı sürekli izleyin
4. **Backup**: Önemli ayarlarınızı yedekleyin

## 📞 Acil Durum

Uygulama donmuşsa:
1. `Ctrl+C` ile durdurun
2. Logları kontrol edin
3. Ayarları gözden geçirin
4. Yeniden başlatın

## 🎮 Test Modu

Gerçek işlem yapmadan test etmek için:
1. Sahte endpoint URL'leri kullanın
2. Loglama seviyesini Debug yapın
3. PollingIntervalMs'i yükseltin

## 📈 İstatistikler

Uygulama çalışırken şu bilgileri takip edebilirsiniz:
- Toplam tespit edilen çekim sayısı
- Başarılı çekim sayısı
- Ortalama yanıt süresi
- Session yenileme sayısı
