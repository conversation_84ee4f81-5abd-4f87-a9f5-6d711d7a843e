﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PaneleCekmeBot.Models;
using PaneleCekmeBot.Services;

namespace PaneleCekmeBot
{
    static class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🤖 Panele Çekme Bot v1.0");
            Console.WriteLine("========================");

            try
            {
                var host = CreateHostBuilder(args).Build();

                // Uygulama başlatılıyor mesajı
                var logger = host.Services.GetRequiredService<ILogger<PaneleCekmeBotWorker>>();
                logger.LogInformation("🚀 Uygulama başlatılıyor...");

                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 Kritik hata: {ex.Message}");
                Console.WriteLine("Detaylar için log dosyalarını kontrol edin.");
                Environment.Exit(1);
            }
        }

        static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // Konfigürasyon
                    services.Configure<AppSettings>(context.Configuration);

                    // Servisler
                    services.AddSingleton<IHttpClientService, HttpClientService>();
                    services.AddSingleton<ILoginService, LoginService>();
                    services.AddSingleton<ICekimMonitoringService, CekimMonitoringService>();
                    services.AddSingleton<IPaneleCekmeService, PaneleCekmeService>();

                    // Worker Service
                    services.AddHostedService<PaneleCekmeBotWorker>();
                })
                .ConfigureLogging((context, logging) =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();

                    // Detaylı loglama için
                    logging.SetMinimumLevel(LogLevel.Debug);
                })
                .UseConsoleLifetime();
    }
}
