{"format": 1, "restore": {"C:\\temp\\PANELE-CEKME\\PaneleCekmeBot\\PaneleCekmeBot.csproj": {}}, "projects": {"C:\\temp\\PANELE-CEKME\\PaneleCekmeBot\\PaneleCekmeBot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\temp\\PANELE-CEKME\\PaneleCekmeBot\\PaneleCekmeBot.csproj", "projectName": "PaneleCekmeBot", "projectPath": "C:\\temp\\PANELE-CEKME\\PaneleCekmeBot\\PaneleCekmeBot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\temp\\PANELE-CEKME\\PaneleCekmeBot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 18.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 18.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"HtmlAgilityPack": {"target": "Package", "version": "[1.12.2, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.8, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}