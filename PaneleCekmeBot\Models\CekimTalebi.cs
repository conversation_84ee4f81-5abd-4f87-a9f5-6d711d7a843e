using Newtonsoft.Json;

namespace PaneleCekmeBot.Models
{
    public class CekimTalebi
    {
        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;

        [JsonProperty("isim")]
        public string Isim { get; set; } = string.Empty;

        [JsonProperty("tutar")]
        public string Tutar { get; set; } = string.Empty;

        [JsonProperty("tarih")]
        public string Tarih { get; set; } = string.Empty;

        public decimal GetTutarAsDecimal()
        {
            if (string.IsNullOrEmpty(Tutar))
                return 0;

            // Tutar string'ini temizle ve decimal'e çevir
            var cleanTutar = Tutar
                .Replace("TL", "")
                .Replace("₺", "")
                .Replace(".", "")  // Binlik ayırıcı
                .Replace(",", ".") // Ondalık ayırıcı
                .Trim();

            if (decimal.TryParse(cleanTutar, System.Globalization.NumberStyles.Any,
                System.Globalization.CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }

            return 0;
        }

        public bool IsValidAmount()
        {
            return GetTutarAsDecimal() > 0;
        }

        public override string ToString()
        {
            return $"ID: {Id}, İsim: {Isim}, Tutar: {Tutar}, Tarih: {Tarih}";
        }
    }
}
