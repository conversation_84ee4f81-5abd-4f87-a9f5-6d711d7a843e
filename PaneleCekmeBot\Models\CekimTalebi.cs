using Newtonsoft.Json;

namespace PaneleCekmeBot.Models
{
    public class CekimTalebi
    {
        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;

        [JsonProperty("isim")]
        public string Isim { get; set; } = string.Empty;

        [JsonProperty("tutar")]
        public string Tutar { get; set; } = string.Empty;

        [JsonProperty("tarih")]
        public string Tarih { get; set; } = string.Empty;

        public override string ToString()
        {
            return $"ID: {Id}, İsim: {Isim}, Tutar: {Tutar}, Tarih: {Tarih}";
        }
    }
}
